
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>交互式网页 - 修复版</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }

        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .demo-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }

        button {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        button:hover {
            background: #ff5252;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255,107,107,0.4);
        }

        input[type="text"] {
            padding: 10px;
            border: none;
            border-radius: 5px;
            margin: 5px;
            font-size: 16px;
        }

        .result {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            min-height: 20px;
        }

        .color-box {
            width: 100px;
            height: 100px;
            border-radius: 10px;
            margin: 10px;
            display: inline-block;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .color-box:hover {
            transform: scale(1.1);
        }

        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 14px;
        }

        .generation-info {
            background: rgba(0,255,0,0.2);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            border: 1px solid rgba(0,255,0,0.3);
        }
    </style>
</head>
<body>
    <div class="status">🔄 动态生成版本</div>

    <div class="container">
        <h1>🎉 JavaScript交互演示 (动态生成版)</h1>

        <div class="generation-info">
            ✅ 此页面由AutoHotkey动态生成于：<span id="generationTime"></span><br>
            🔄 每次按F3都会重新生成最新版本
        </div>

        <div class="demo-section">
            <h3>1. 简单计算器</h3>
            <input type="text" id="num1" placeholder="输入第一个数字" value="10">
            <input type="text" id="num2" placeholder="输入第二个数字" value="5">
            <br>
            <button onclick="calculate('+')">加法 +</button>
            <button onclick="calculate('-')">减法 -</button>
            <button onclick="calculate('*')">乘法 ×</button>
            <button onclick="calculate('/')">除法 ÷</button>
            <div class="result" id="calcResult">计算结果将显示在这里</div>
        </div>

        <div class="demo-section">
            <h3>2. 文字效果</h3>
            <input type="text" id="textInput" placeholder="输入一些文字" value="Hello AutoHotkey!">
            <button onclick="showText()">显示文字</button>
            <button onclick="clearText()">清空</button>
            <div class="result" id="textResult"></div>
        </div>

        <div class="demo-section">
            <h3>3. 颜色变换</h3>
            <p>点击下面的色块改变背景颜色：</p>
            <div class="color-box" style="background: #ff6b6b;" onclick="changeBackground('#ff6b6b')"></div>
            <div class="color-box" style="background: #4ecdc4;" onclick="changeBackground('#4ecdc4')"></div>
            <div class="color-box" style="background: #45b7d1;" onclick="changeBackground('#45b7d1')"></div>
            <div class="color-box" style="background: #96ceb4;" onclick="changeBackground('#96ceb4')"></div>
            <div class="color-box" style="background: #feca57;" onclick="changeBackground('#feca57')"></div>
        </div>

        <div class="demo-section">
            <h3>4. 当前时间</h3>
            <button onclick="showTime()">显示当前时间</button>
            <button onclick="startClock()">开始时钟</button>
            <button onclick="stopClock()">停止时钟</button>
            <div class="result" id="timeResult"></div>
        </div>

        <div class="demo-section">
            <h3>5. 随机功能</h3>
            <button onclick="randomColor()">随机颜色</button>
            <button onclick="randomNumber()">随机数字</button>
            <button onclick="randomQuote()">随机名言</button>
            <div class="result" id="randomResult"></div>
        </div>

        <div class="demo-section">
            <h3>6. 测试所有功能</h3>
            <button onclick="testAllFunctions()">🧪 一键测试所有功能</button>
            <div class="result" id="testResult"></div>
        </div>
    </div>

    <script>
        let clockInterval = null;

        // 显示生成时间
        document.getElementById("generationTime").textContent = new Date().toLocaleString("zh-CN");

        // 计算器功能
        function calculate(operation) {
            const num1 = parseFloat(document.getElementById("num1").value);
            const num2 = parseFloat(document.getElementById("num2").value);
            const result = document.getElementById("calcResult");

            if (isNaN(num1) || isNaN(num2)) {
                result.innerHTML = "❌ 请输入有效的数字！";
                return;
            }

            let answer;
            switch(operation) {
                case "+": answer = num1 + num2; break;
                case "-": answer = num1 - num2; break;
                case "*": answer = num1 * num2; break;
                case "/":
                    if (num2 === 0) {
                        result.innerHTML = "❌ 不能除以零！";
                        return;
                    }
                    answer = num1 / num2;
                    break;
            }

            result.innerHTML = `✅ ${num1} ${operation} ${num2} = ${answer}`;
        }

        // 文字显示功能
        function showText() {
            const text = document.getElementById("textInput").value;
            const result = document.getElementById("textResult");

            if (text.trim() === "") {
                result.innerHTML = "请输入一些文字！";
                return;
            }

            result.innerHTML = `<h2 style="color: #feca57;">🎨 ${text}</h2>`;
        }

        function clearText() {
            document.getElementById("textResult").innerHTML = "";
            document.getElementById("textInput").value = "";
        }

        // 背景颜色变换
        function changeBackground(color) {
            document.body.style.background = `linear-gradient(135deg, ${color} 0%, #764ba2 100%)`;
        }

        // 时间功能
        function showTime() {
            const now = new Date();
            document.getElementById("timeResult").innerHTML =
                `🕐 当前时间：${now.toLocaleString("zh-CN")}`;
        }

        function startClock() {
            if (clockInterval) clearInterval(clockInterval);

            clockInterval = setInterval(() => {
                const now = new Date();
                document.getElementById("timeResult").innerHTML =
                    `⏰ 实时时钟：${now.toLocaleString("zh-CN")}`;
            }, 1000);
        }

        function stopClock() {
            if (clockInterval) {
                clearInterval(clockInterval);
                clockInterval = null;
                document.getElementById("timeResult").innerHTML += " (已停止)";
            }
        }

        // 随机功能
        function randomColor() {
            const colors = ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57", "#ff9ff3", "#54a0ff"];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            document.getElementById("randomResult").innerHTML =
                `🎨 随机颜色：<span style="background: ${randomColor}; padding: 5px 10px; border-radius: 5px;">${randomColor}</span>`;
        }

        function randomNumber() {
            const randomNum = Math.floor(Math.random() * 1000) + 1;
            document.getElementById("randomResult").innerHTML = `🎲 随机数字：${randomNum}`;
        }

        function randomQuote() {
            const quotes = [
                "生活就像骑自行车，要保持平衡，就必须不断前进。",
                "成功不是终点，失败也不是末日，重要的是继续前进的勇气。",
                "学习永远不晚，知识是最好的投资。",
                "代码改变世界，创意点亮未来。",
                "每一个不曾起舞的日子，都是对生命的辜负。"
            ];
            const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
            document.getElementById("randomResult").innerHTML = `💭 随机名言：<em>"${randomQuote}"</em>`;
        }

        // 测试所有功能
        function testAllFunctions() {
            const testResult = document.getElementById("testResult");
            testResult.innerHTML = "🧪 正在测试所有功能...";

            let testCount = 0;
            const totalTests = 5;

            // 测试计算器
            setTimeout(() => {
                calculate('+');
                testCount++;
                updateTestProgress(testCount, totalTests, testResult);
            }, 500);

            // 测试文字显示
            setTimeout(() => {
                document.getElementById("textInput").value = "测试文字";
                showText();
                testCount++;
                updateTestProgress(testCount, totalTests, testResult);
            }, 1000);

            // 测试颜色变换
            setTimeout(() => {
                changeBackground('#4ecdc4');
                testCount++;
                updateTestProgress(testCount, totalTests, testResult);
            }, 1500);

            // 测试时间显示
            setTimeout(() => {
                showTime();
                testCount++;
                updateTestProgress(testCount, totalTests, testResult);
            }, 2000);

            // 测试随机功能
            setTimeout(() => {
                randomNumber();
                testCount++;
                updateTestProgress(testCount, totalTests, testResult);

                setTimeout(() => {
                    testResult.innerHTML = "🎉 所有功能测试完成！一切正常运行。";
                }, 500);
            }, 2500);
        }

        function updateTestProgress(current, total, element) {
            const percentage = Math.round((current / total) * 100);
            element.innerHTML = `🧪 测试进度：${current}/${total} (${percentage}%)`;
        }

        // 页面加载完成后的欢迎消息
        window.addEventListener("load", function() {
            setTimeout(() => {
                console.log("✅ 页面加载完成，动态生成版本");

                // 显示欢迎消息
                alert("🎉 欢迎体验AutoHotkey动态生成的交互式网页！\n\n🔄 此版本每次按F3都会重新生成\n✅ 所有JavaScript功能正常\n🧪 包含完整的测试功能\n\n快来试试各种功能吧！");
            }, 1000);
        });

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        calculate('+');
                        break;
                    case '2':
                        e.preventDefault();
                        showText();
                        break;
                    case '3':
                        e.preventDefault();
                        showTime();
                        break;
                    case '4':
                        e.preventDefault();
                        randomNumber();
                        break;
                    case 't':
                        e.preventDefault();
                        testAllFunctions();
                        break;
                }
            }
        });
    </script>
</body>
</html>








